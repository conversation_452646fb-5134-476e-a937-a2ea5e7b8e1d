.page-container {
  min-height: 100vh;
  background-color: #f8f9fa;
  padding: 30rpx 20rpx;
  box-sizing: border-box;
}

.header {
  margin-bottom: 40rpx;
  text-align: center;
}

.title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  position: relative;
  padding-bottom: 20rpx;
}

.title::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 6rpx;
  background-color: #4080ff;
  border-radius: 6rpx;
}

/* 餐点选项卡片样式 */
.meal-options {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
  padding: 10rpx;
  width: 92%;
  margin: 0 auto;
}

.meal-card {
  display: flex;
  align-items: center;
  padding: 50rpx 50rpx;
  width: 100%;
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.05);
  position: relative;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  box-sizing: border-box;
}

.meal-card:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.03);
}

.meal-icon-container {
  width: 135rpx;
  height: 135rpx;
  border-radius: 50%;
  background: rgba(255, 165, 0, 0.15);
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 30rpx;
  flex-shrink: 0;
}

.business {
  background: rgba(0, 191, 255, 0.15);
}

.vegan {
  background: rgba(60, 179, 113, 0.15);
}

.onsite {
  background: rgba(144, 238, 144, 0.15);
}

.meal-icon {
  font-size: 48rpx;
}

.meal-content {
  flex: 1;
}

.meal-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 10rpx;
  display: block;
}

.meal-desc {
  font-size: 24rpx;
  color: #888;
  display: block;
}

.arrow {
  font-size: 40rpx;
  color: #ccc;
  font-weight: 300;
  margin-left: 20rpx;
}

/* 弹窗样式优化 */
.dialog-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999;
  animation: fadeIn 0.2s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.dialog-container {
  background: white;
  border-radius: 20rpx;
  width: 80%;
  padding: 50rpx 30rpx 60rpx;
  position: relative;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
  transform: scale(0.9);
  animation: scaleIn 0.3s ease forwards;
}

@keyframes scaleIn {
  to {
    transform: scale(1);
  }
}

.dialog-header {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  margin-bottom: 50rpx;
}

.dialog-title {
  text-align: center;
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.close-btn {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 50rpx;
  height: 50rpx;
  line-height: 50rpx;
  text-align: center;
  font-size: 48rpx;
  color: #999;
}

.meal-type-container {
  display: flex;
  justify-content: space-between;
  gap: 30rpx;
}

.meal-type-btn {
  flex: 1;
  background: #f8f9fa;
  border: none;
  padding: 40rpx 20rpx;
  border-radius: 16rpx;
  color: #333;
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  min-height: 180rpx;
}

.meal-type-btn::after {
  border: none;
}

.btn-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.btn-icon {
  font-size: 60rpx;
  margin-bottom: 20rpx;
}

.btn-text {
  font-size: 30rpx;
  font-weight: 500;
}

.meal-type-btn.active {
  background-color: #4080ff;
  color: white;
  transform: translateY(-4rpx);
  box-shadow: 0 10rpx 20rpx rgba(60, 179, 113, 0.3);
}

/* 红色提示语样式 */
.red-tip {
  color: #ff4444;
  font-size: 24rpx;
  text-align: center;
  margin-top: -20rpx;
  margin-bottom: 20rpx;
}

/* 加载状态样式 */
.loading-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #4080ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 30rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 32rpx;
  color: #666;
  font-weight: 500;
}

/* 推荐时段卡片样式 */
.recommended-card {
  margin: 0 20rpx 30rpx;
  background: linear-gradient(135deg, #007aff 0%, #007aff 100%);
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  color: white;
  box-shadow: 0 10rpx 30rpx rgba(102, 126, 234, 0.3);
}

.recommended-header {
  display: flex;
  align-items: center;
  margin-bottom: 25rpx;
}

.recommended-icon {
  font-size: 32rpx;
  margin-right: 15rpx;
}

.recommended-title {
  font-size: 32rpx;
  font-weight: 600;
}

.recommended-content {
  margin-bottom: 30rpx;
}

.recommended-date {
  font-size: 28rpx;
  opacity: 0.9;
  margin-bottom: 10rpx;
}

.recommended-time {
  font-size: 48rpx;
  font-weight: 600;
  margin-bottom: 20rpx;
}

.recommended-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.product-name {
  font-size: 30rpx;
  font-weight: 500;
}

.price {
  font-size: 36rpx;
  font-weight: 600;
  color: #ffeb3b;
}

.remaining-info {
  font-size: 26rpx;
  opacity: 0.8;
}

.select-recommended-btn {
  width: 100%;
  background: rgba(255, 255, 255, 0.2);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 25rpx;
  border-radius: 50rpx;
  font-size: 32rpx;
  font-weight: 500;
  backdrop-filter: blur(10rpx);
}

.select-recommended-btn::after {
  border: none;
}

.select-recommended-btn:active {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(0.98);
}

/* 无推荐时段提示 */
.no-recommendation {
  margin: 0 20rpx 30rpx;
  padding: 50rpx 30rpx;
  background: #fff3cd;
  border: 2rpx solid #ffc107;
  border-radius: 16rpx;
  text-align: center;
}

.no-recommendation-text {
  font-size: 28rpx;
  color: #856404;
}

/* 配置结果页面样式 */
.config-result-container {
  min-height: 100vh;
  background-color: #f8f9fa;
  padding: 30rpx 0;
}

.config-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  padding: 0 30rpx;
}

.config-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.back-btn {
  background: #f0f0f0;
  border: none;
  padding: 15rpx 30rpx;
  border-radius: 50rpx;
  font-size: 28rpx;
  color: #666;
}

.back-btn::after {
  border: none;
}

.config-content {
  background: white;
  margin: 0 20rpx;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.config-section {
  padding: 40rpx 30rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.config-section:last-child {
  border-bottom: none;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

/* 费用样式 */
.fee-amount {
  font-size: 48rpx;
  font-weight: 600;
  color: #ff6b6b;
  text-align: center;
  padding: 20rpx 0;
}

/* 日期列表样式 */
.date-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.date-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25rpx 30rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}

.date-item.available {
  background: #e8f5e8;
  border: 2rpx solid #4caf50;
}

.date-item.available:active {
  transform: scale(0.98);
  background: #d4edda;
}

.date-item.unavailable {
  background: #fff2f2;
  border: 2rpx solid #ff6b6b;
  opacity: 0.6;
}

.date-info {
  flex: 1;
}

.date-text {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.available-slots-count {
  font-size: 24rpx;
  color: #666;
}

.date-item.available .available-slots-count {
  color: #4caf50;
}

.date-item.unavailable .available-slots-count {
  color: #ff6b6b;
}

.date-arrow {
  font-size: 32rpx;
  color: #ccc;
  font-weight: 300;
}

.date-item.available .date-arrow {
  color: #4caf50;
}

/* 企业列表样式 */
.enterprise-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.enterprise-item {
  padding: 25rpx 30rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border-left: 6rpx solid #4080ff;
}

.enterprise-name {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 10rpx;
}

.enterprise-info {
  font-size: 26rpx;
  color: #666;
}

/* 已预约日期样式 */
.already-date-list {
  display: flex;
  flex-wrap: wrap;
  gap: 15rpx;
}

.already-date-item {
  padding: 15rpx 25rpx;
  background: #fff3cd;
  border: 2rpx solid #ffc107;
  border-radius: 30rpx;
  font-size: 26rpx;
  color: #856404;
}

/* 时段列表样式 */
.time-slots-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.time-slot-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  transition: all 0.3s ease;
  position: relative;
}

.time-slot-item.available {
  background: #e8f5e8;
  border: 2rpx solid #4caf50;
  cursor: pointer;
}

.time-slot-item.available:active {
  transform: scale(0.98);
  background: #d4edda;
}

.time-slot-item.unavailable {
  background: #fff2f2;
  border: 2rpx solid #ff6b6b;
  opacity: 0.6;
}

.time-slot-item.recommended {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  color: white;
  border: 2rpx solid #667eea;
}

.slot-time {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  min-width: 200rpx;
}

.time-slot-item.recommended .slot-time {
  color: white;
}

.slot-info {
  flex: 1;
  margin-left: 30rpx;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.slot-product {
  font-size: 28rpx;
  color: #333;
}

.slot-price {
  font-size: 28rpx;
  font-weight: 600;
  color: #ff6b6b;
}

.time-slot-item.recommended .slot-product,
.time-slot-item.recommended .slot-price {
  color: white;
}

.slot-capacity {
  font-size: 24rpx;
  color: #666;
  margin-left: 20rpx;
}

.time-slot-item.available .slot-capacity {
  color: #4caf50;
}

.time-slot-item.unavailable .slot-capacity {
  color: #ff6b6b;
}

.time-slot-item.recommended .slot-capacity {
  color: white;
}

.slot-badge {
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  background: #ffeb3b;
  color: #333;
  padding: 6rpx 15rpx;
  border-radius: 20rpx;
  font-size: 20rpx;
  font-weight: 600;
}

/* 人数选择器样式 */
.people-selector {
  margin-top: 30rpx;
  padding: 30rpx 0;
  border-top: 2rpx solid rgba(255, 255, 255, 0.2);
}

.people-label {
  font-size: 28rpx;
  color: white;
  opacity: 0.9;
  margin-bottom: 20rpx;
  text-align: center;
}

.people-counter {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 30rpx;
}

.counter-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  color: white;
  font-size: 32rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
}

.counter-btn::after {
  border: none;
}

.counter-btn:active {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(0.95);
}

.people-input {
  width: 100rpx;
  height: 60rpx;
  background: rgba(255, 255, 255, 0.2);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 30rpx;
  color: white;
  font-size: 32rpx;
  font-weight: 600;
  text-align: center;
  backdrop-filter: blur(10rpx);
}

/* 支付按钮样式 - 修改为绿色主题 */
.select-recommended-btn {
  width: 100%;
  background: linear-gradient(135deg, #34c759 0%, #34c759 100%);
  border: none;
  color: white;
  padding: 30rpx 25rpx;
  border-radius: 50rpx;
  font-size: 34rpx;
  font-weight: 600;
  margin-top: 40rpx;
  box-shadow: 0 8rpx 20rpx rgba(76, 175, 80, 0.3);
  transition: all 0.3s ease;
}

.select-recommended-btn::after {
  border: none;
}

.select-recommended-btn:active {
  background: linear-gradient(135deg, #34c759 0%, #34c759 100%);
  transform: scale(0.98);
  box-shadow: 0 6rpx 15rpx rgba(76, 175, 80, 0.4);
}

.select-recommended-btn.processing {
  background: linear-gradient(135deg, #34c759 0%, #34c759 100%);
  opacity: 0.8;
  transform: none;
  box-shadow: 0 4rpx 10rpx rgba(76, 175, 80, 0.2);
}

.select-recommended-btn[disabled] {
  opacity: 0.6;
  transform: none;
}

/* 为支付按钮添加图标样式 */
.payment-btn-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15rpx;
}

.payment-icon {
  font-size: 32rpx;
}

/* 无推荐时段提示优化 */
.no-recommendation {
  margin: 0 20rpx 30rpx;
  padding: 80rpx 30rpx;
  background: #007aff;
  border: 2rpx solid #007aff;
  border-radius: 16rpx;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20rpx;
}

.no-meal-icon {
  font-size: 80rpx;
  opacity: 0.7;
}

.no-recommendation-text {
  font-size: 36rpx;
  color: rgba(255, 255, 255, 0.3);
  font-weight: 600;
}

.no-recommendation-desc {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.3);
  opacity: 0.8;
}

/* 无餐弹窗样式 */
.no-meal-dialog-content {
  text-align: center;
  padding: 60rpx 40rpx;
}

.no-meal-icon {
  font-size: 100rpx;
  margin-bottom: 30rpx;
  opacity: 0.8;
}

.no-meal-title {
  font-size: 36rpx;
  color: #333;
  font-weight: 600;
  margin-bottom: 20rpx;
  display: block;
}

.no-meal-desc {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 40rpx;
  display: block;
  line-height: 1.5;
}

.confirm-btn {
  background: #4080ff;
  color: white;
  border: none;
  padding: 25rpx 80rpx;
  border-radius: 50rpx;
  font-size: 32rpx;
  font-weight: 500;
  min-width: 200rpx;
}

.confirm-btn::after {
  border: none;
}

.confirm-btn:active {
  background: #3366cc;
  transform: scale(0.98);
}

/* 推荐时段卡片居中容器 */
.recommended-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 70vh;
  padding: 40rpx 20rpx;
}

/* 修改推荐时段卡片样式，去掉原来的margin */
.recommended-card {
  background: linear-gradient(135deg, #007aff 0%, #007aff 100%);
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  color: white;
  box-shadow: 0 10rpx 30rpx rgba(102, 126, 234, 0.3);
  width: 100%;
  max-width: 600rpx;
}

/* 无餐卡片样式 - 与推荐卡片保持一致的蓝色背景 */
.no-meal-card {
  background: linear-gradient(135deg, #007aff 0%, #007aff 100%);
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  color: white;
  box-shadow: 0 10rpx 30rpx rgba(102, 126, 234, 0.3);
  width: 100%;
  max-width: 600rpx;
}

.no-meal-header {
  display: flex;
  align-items: center;
  margin-bottom: 25rpx;
}

.no-meal-icon {
  font-size: 32rpx;
  margin-right: 15rpx;
}

.no-meal-title {
  font-size: 32rpx;
  font-weight: 600;
}

.no-meal-content {
  margin-bottom: 30rpx;
}

.no-meal-date {
  font-size: 28rpx;
  opacity: 0.9;
  margin-bottom: 10rpx;
}

.no-meal-message {
  font-size: 48rpx;
  font-weight: 600;
  margin-bottom: 20rpx;
}

.no-meal-desc {
  font-size: 26rpx;
  opacity: 0.8;
}

/* 无餐确认按钮 - 使用绿色样式与推荐卡片按钮保持一致 */
.confirm-no-meal-btn {
  width: 80%; /* 修改：从100%改为80%，让按钮更小一些 */
  background: linear-gradient(135deg, #34c759 0%, #34c759 100%);
  border: none;
  color: white;
  padding: 25rpx 20rpx; /* 修改：从30rpx 25rpx改为25rpx 20rpx，减少内边距 */
  border-radius: 50rpx;
  font-size: 32rpx; /* 修改：从34rpx改为32rpx，稍微减小字体 */
  font-weight: 600;
  margin-top: 40rpx;
  margin-left: auto; /* 新增：让按钮居中 */
  margin-right: auto; /* 新增：让按钮居中 */
  box-shadow: 0 8rpx 20rpx rgba(76, 175, 80, 0.3);
  transition: all 0.3s ease;
}

.confirm-no-meal-btn::after {
  border: none;
}

.confirm-no-meal-btn:active {
  background: linear-gradient(135deg, #34c759 0%, #34c759 100%);
  transform: scale(0.98);
  box-shadow: 0 6rpx 15rpx rgba(76, 175, 80, 0.4);
}

.confirm-btn-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15rpx;
}

/* 总金额显示样式 */
.total-amount {
  margin-top: 30rpx;
  padding: 20rpx 0;
  border-top: 2rpx solid rgba(255, 255, 255, 0.2);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.amount-label {
  font-size: 28rpx;
  color: white;
  opacity: 0.9;
}

.amount-value {
  font-size: 36rpx;
  font-weight: 600;
  color: #ffeb3b;
}

/* 支付弹窗样式 */
.payment-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999;
  animation: fadeIn 0.2s ease;
}

.payment-container {
  background: white;
  border-radius: 20rpx;
  width: 85%;
  max-width: 600rpx;
  padding: 40rpx 30rpx;
  position: relative;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
}

.payment-header {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  margin-bottom: 30rpx;
}

.payment-header text {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.close-icon {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 50rpx;
  height: 50rpx;
  line-height: 50rpx;
  text-align: center;
  font-size: 40rpx;
  color: #999;
  cursor: pointer;
}

.payment-amount {
  text-align: center;
  font-size: 48rpx;
  font-weight: 600;
  color: #ff6b6b;
  margin-bottom: 40rpx;
}

.payment-methods {
  margin-bottom: 40rpx;
}

.payment-methods > text {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 20rpx;
  display: block;
}

.payment-method {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25rpx 20rpx;
  margin-bottom: 20rpx;
  border: 2rpx solid #f0f0f0;
  border-radius: 12rpx;
  transition: all 0.3s ease;
  position: relative;
}

.payment-method.selected {
  border-color: #4080ff;
  background-color: #f8fafe;
}

.payment-method.disabled {
  opacity: 0.5;
  background-color: #f9f9f9;
}

.payment-method-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.method-icon {
  width: 50rpx;
  height: 50rpx;
  margin-right: 20rpx;
}

.payment-method-text {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
}

.balance-info {
  margin-left: 20rpx;
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.balance-text {
  font-size: 24rpx;
  color: #666;
}

.balance-amount {
  font-size: 24rpx;
  color: #4080ff;
  font-weight: 600;
}

.radio-container {
  display: flex;
  align-items: center;
}

.radio-btn {
  width: 30rpx;
  height: 30rpx;
  border: 2rpx solid #ddd;
  border-radius: 50%;
  position: relative;
}

.payment-method.selected .radio-btn {
  border-color: #4080ff;
}

.payment-method.selected .radio-btn::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 16rpx;
  height: 16rpx;
  background-color: #4080ff;
  border-radius: 50%;
}

.insufficient-tip {
  position: absolute;
  right: 80rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 24rpx;
  color: #ff6b6b;
}

/* 企业选择列表样式 */
.enterprise-list {
  margin-top: 30rpx;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
}

.enterprise-label {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 20rpx;
  display: block;
}

.enterprise-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  margin-bottom: 15rpx;
  background: white;
  border-radius: 8rpx;
  border: 2rpx solid #f0f0f0;
  transition: all 0.3s ease;
}

.enterprise-item.selected {
  border-color: #4080ff;
  background-color: #f8fafe;
}

.enterprise-name {
  font-size: 28rpx;
  color: #333;
}

.no-enterprise {
  text-align: center;
  padding: 40rpx;
  color: #999;
  font-size: 26rpx;
}

/* 确认支付按钮 */
.confirm-payment-btn {
  width: 100%;
  background: linear-gradient(135deg, #4080ff 0%, #4080ff 100%);
  border: none;
  color: white;
  padding: 30rpx 25rpx;
  border-radius: 50rpx;
  font-size: 32rpx;
  font-weight: 600;
  box-shadow: 0 8rpx 20rpx rgba(64, 128, 255, 0.3);
  transition: all 0.3s ease;
}

.confirm-payment-btn::after {
  border: none;
}

.confirm-payment-btn:active {
  transform: scale(0.98);
  box-shadow: 0 6rpx 15rpx rgba(64, 128, 255, 0.4);
}

.confirm-payment-btn.processing {
  opacity: 0.8;
  transform: none;
  box-shadow: 0 4rpx 10rpx rgba(64, 128, 255, 0.2);
}

.confirm-payment-btn[disabled] {
  opacity: 0.6;
  transform: none;
}

/* 企业选择列表中的radio-btn选中状态 */
.enterprise-item.selected .radio-btn {
  border-color: #4080ff;
}

.enterprise-item.selected .radio-btn::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 16rpx;
  height: 16rpx;
  background-color: #4080ff;
  border-radius: 50%;
}

.payment-method-info {
  display: flex;
  flex-direction: column;
}

.payment-method-desc {
  font-size: 12px;
  color: #999;
  margin-top: 2px;
}

.payment-notice {
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 6px;
  margin: 10px 0;
}

.notice-text {
  display: block;
  font-size: 13px;
  color: #666;
  line-height: 1.4;
}

/* 企业选择相关样式 */
.enterprise-selection {
  margin-top: 15px;
}

.enterprise-label {
  font-size: 14px;
  color: #333;
  font-weight: bold;
  display: block;
  margin-bottom: 10px;
}

.enterprise-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  margin-bottom: 8px;
  background-color: #fff;
  transition: all 0.3s ease;
}

.enterprise-item.selected {
  border-color: #4CAF50;
  background-color: #f8fff8;
}

.enterprise-info {
  flex: 1;
}

.enterprise-name {
  font-size: 15px;
  color: #333;
  font-weight: 500;
}

.no-enterprise {
  text-align: center;
  padding: 20px;
  color: #999;
  font-size: 14px;
}

/* 支付说明样式 */
.payment-notice {
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 6px;
  margin: 10px 0;
  border-left: 3px solid #4CAF50;
}

.notice-text {
  display: block;
  font-size: 13px;
  color: #666;
  line-height: 1.5;
}

.notice-text:first-child {
  margin-bottom: 4px;
}

/* 合并支付弹窗样式 */
.combined-payment-container {
  background: white;
  border-radius: 20rpx;
  width: 90%;
  max-width: 700rpx;
  padding: 40rpx 30rpx;
  position: relative;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
  max-height: 80vh;
  overflow-y: auto;
}

/* 支付步骤样式 */
.payment-section {
  margin-bottom: 30rpx;
  border-radius: 12rpx;
  overflow: hidden;
  border: 2rpx solid #f0f0f0;
  transition: all 0.3s ease;
}

.payment-section.disabled {
  opacity: 0.5;
  pointer-events: none;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 25rpx;
  background-color: #f8f9fa;
  border-bottom: 2rpx solid #f0f0f0;
}

.section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.section-status {
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  font-weight: 500;
  background-color: #fff3cd;
  color: #856404;
}

.section-status.completed {
  background-color: #d4edda;
  color: #155724;
}

.section-content {
  padding: 30rpx 25rpx;
}

.section-content.completed {
  background-color: #f8fff8;
}

.enterprise-amount,
.personal-amount {
  text-align: center;
  font-size: 40rpx;
  font-weight: 600;
  color: #ff6b6b;
  margin-bottom: 25rpx;
}

/* 企业支付完成信息 */
.payment-completed-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx 0;
}

.completed-icon {
  width: 80rpx;
  height: 80rpx;
  background-color: #28a745;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  font-weight: 600;
  margin-bottom: 15rpx;
}

.completed-text {
  font-size: 28rpx;
  color: #28a745;
  font-weight: 500;
}

/* 等待提示 */
.waiting-tip {
  text-align: center;
  padding: 40rpx 0;
  color: #999;
  font-size: 26rpx;
}

/* 分隔线 */
.payment-divider {
  height: 2rpx;
  background: linear-gradient(to right, transparent, #e0e0e0 50%, transparent);
  margin: 30rpx 0;
}

/* 步骤支付按钮 */
.section-payment-btn {
  width: 100%;
  border: none;
  color: white;
  padding: 25rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 600;
  margin-top: 25rpx;
  transition: all 0.3s ease;
  background: linear-gradient(135deg, #4080ff 0%, #4080ff 100%);
}

.section-payment-btn::after {
  border: none;
}

.section-payment-btn:active {
  transform: scale(0.98);
}

.section-payment-btn.completed {
  background: linear-gradient(135deg, #28a745 0%, #28a745 100%);
}

.section-payment-btn.disabled {
  background: #e0e0e0;
  color: #999;
  transform: none;
}

.section-payment-btn.processing {
  opacity: 0.8;
  transform: none;
}

.section-payment-btn[disabled] {
  opacity: 0.6;
  transform: none;
}

/* 企业选择在合并支付中的样式调整 */
.payment-section .enterprise-selection {
  margin-top: 20rpx;
}

.payment-section .enterprise-label {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 15rpx;
  display: block;
}

.payment-section .enterprise-item {
  padding: 18rpx 20rpx;
  margin-bottom: 12rpx;
}

.payment-section .enterprise-name {
  font-size: 26rpx;
}

/* 个人支付方式在合并支付中的样式调整 */
.payment-section .payment-methods > text {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 15rpx;
  display: block;
}

.payment-section .payment-method {
  padding: 20rpx 18rpx;
  margin-bottom: 15rpx;
}

.payment-section .payment-method-text {
  font-size: 26rpx;
}

.payment-section .balance-text,
.payment-section .balance-amount {
  font-size: 22rpx;
}
