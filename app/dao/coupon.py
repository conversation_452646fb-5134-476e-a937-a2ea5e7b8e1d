from typing import List, Optional, Union

from sqlalchemy import or_
from sqlalchemy.orm import Session

from app.dao.base import DAO
from app.models import User
from app.models.coupon import Coupon, CouponType, CouponUsageRecord, DiscountCoupon, CashCoupon, FullReductionCoupon
from app.models.enum import Status
from app.schemas.coupon import (
    CouponCreate, CouponUpdate, CouponUsageRecordSearch, CouponUsageRecordCreate,
    DiscountCouponCreate, CashCouponCreate, FullReductionCouponCreate
)


class CouponDAO(DAO):
    """优惠券数据访问对象"""

    def __init__(self):
        super().__init__(Coupon)

    def create(self, session: Session, coupon: Union[
        CouponCreate, DiscountCouponCreate, CashCouponCreate, FullReductionCouponCreate]) -> Coupon:
        """创建优惠券
        
        Args:
            session: 数据库会话
            coupon: 优惠券创建请求，可以是基础优惠券或特定类型优惠券
            
        Returns:
            创建的优惠券实例
        """
        coupon_data = coupon.model_dump()
        coupon_type = coupon_data.get('type')

        # 根据优惠券类型选择对应的模型类
        model_class = {
            CouponType.DISCOUNT: DiscountCoupon,
            CouponType.CASH: CashCoupon,
            CouponType.FULL_REDUCTION: FullReductionCoupon,
            CouponType.COUPON: Coupon
        }.get(coupon_type, Coupon)

        # 创建优惠券实例
        instance = model_class(**coupon_data)
        session.add(instance)
        session.commit()
        session.refresh(instance)
        return instance

    def get(self, session: Session, coupon_id: int) -> Optional[Coupon]:
        """根据ID获取优惠券"""
        return super().get(session, coupon_id)

    def get_list(self, session: Session, skip: int = 0, limit: int = 100) -> List[Coupon]:
        """获取优惠券列表"""
        return session.query(self.model).offset(skip).limit(limit).all()

    def update(self, session: Session, coupon_id: int, coupon: CouponUpdate) -> Optional[Coupon]:
        """更新优惠券"""
        coupon_data = coupon.model_dump(exclude_unset=True)
        return super().update(session, coupon_id, **coupon_data)

    def delete(self, session: Session, coupon_id: int) -> bool:
        """删除优惠券"""
        return super().delete(session, coupon_id)

    def search(self,
               session: Session,
               keyword: Optional[str] = None,
               status: Optional[Status] = None,
               coupon_type: Optional[CouponType] = None,
               skip: int = 0,
               limit: int = 10) -> dict:
        """搜索优惠券
        
        Args:
            session: 数据库会话
            keyword: 搜索关键词（匹配名称和描述）
            status: 优惠券状态
            coupon_type: 优惠券类型
            skip: 分页起始位置
            limit: 每页数量
            
        Returns:
            dict: 包含 'total'（搜索结果总数）和 'list'（分页后的优惠券列表）的字典
        """
        query = session.query(self.model)

        if keyword:
            query = query.filter(
                or_(
                    self.model.name.ilike(f"%{keyword}%"),
                    self.model.description.ilike(f"%{keyword}%")
                )
            )

        if status:
            query = query.filter(self.model.status == status)

        if coupon_type:
            query = query.filter(self.model.type == coupon_type)

        total = query.count()
        items = query.offset(skip).limit(limit).all()

        return {
            "total": total,
            "list": items
        }

    def update_status(self, session: Session, coupon_id: int, status: Status) -> Optional[Coupon]:
        """更新优惠券状态
        
        Args:
            session: 数据库会话
            coupon_id: 优惠券ID
            status: 新的状态
            
        Returns:
            更新后的优惠券对象，如果优惠券不存在则返回 None
        """
        return super().update(session, coupon_id, status=status)

    def search_by_name(self, session: Session, name: str) -> List[dict]:
        """
        根据优惠券名称进行模糊搜索
        
        Args:
            session: 数据库会话
            name: 优惠券名称关键词
            
        Returns:
            List[dict]: 包含优惠券名称和ID的列表
        """
        search_name = f"%{name}%"
        coupons = session.query(self.model).filter(
            self.model.name.like(search_name),
            self.model.status == Status.ACTIVE
        ).all()
        
        return [{"name": coupon.name, "id": coupon.id} for coupon in coupons]


class DiscountCouponDAO(DAO):
    """折扣券数据访问对象"""

    def __init__(self):
        super().__init__(DiscountCoupon)

    def create(self, session: Session, coupon: DiscountCouponCreate) -> DiscountCoupon:
        """创建折扣券"""
        coupon_data = coupon.model_dump()
        return super().create(session, **coupon_data)

    def get(self, session: Session, coupon_id: int) -> Optional[DiscountCoupon]:
        """根据ID获取折扣券"""
        return super().get(session, coupon_id)

    def update(self, session: Session, coupon_id: int, coupon: DiscountCouponCreate) -> Optional[DiscountCoupon]:
        """更新折扣券"""
        coupon_data = coupon.model_dump(exclude_unset=True)
        if 'type' in coupon_data:
            # 不允许修改type
            del coupon_data['type']
        return super().update(session, coupon_id, **coupon_data)

    def delete(self, session: Session, coupon_id: int) -> bool:
        """删除折扣券"""
        return super().delete(session, coupon_id)


class CashCouponDAO(DAO):
    """现金券数据访问对象"""

    def __init__(self):
        super().__init__(CashCoupon)

    def create(self, session: Session, coupon: CashCouponCreate) -> CashCoupon:
        """创建现金券"""
        coupon_data = coupon.model_dump()
        return super().create(session, **coupon_data)

    def get(self, session: Session, coupon_id: int) -> Optional[CashCoupon]:
        """根据ID获取现金券"""
        return super().get(session, coupon_id)

    def update(self, session: Session, coupon_id: int, coupon: CashCouponCreate) -> Optional[CashCoupon]:
        """更新现金券"""
        coupon_data = coupon.model_dump(exclude_unset=True)
        if 'type' in coupon_data:
            # 不允许修改type
            del coupon_data['type']
        return super().update(session, coupon_id, **coupon_data)

    def delete(self, session: Session, coupon_id: int) -> bool:
        """删除现金券"""
        return super().delete(session, coupon_id)


class FullReductionCouponDAO(DAO):
    """满减券数据访问对象"""

    def __init__(self):
        super().__init__(FullReductionCoupon)

    def create(self, session: Session, coupon: FullReductionCouponCreate) -> FullReductionCoupon:
        """创建满减券"""
        coupon_data = coupon.model_dump()
        return super().create(session, **coupon_data)

    def get(self, session: Session, coupon_id: int) -> Optional[FullReductionCoupon]:
        """根据ID获取满减券"""
        return super().get(session, coupon_id)

    def update(self, session: Session, coupon_id: int, coupon: FullReductionCouponCreate) -> Optional[
        FullReductionCoupon]:
        """更新满减券"""
        coupon_data = coupon.model_dump(exclude_unset=True)
        if 'type' in coupon_data:
            # 不允许修改type
            del coupon_data['type']
        return super().update(session, coupon_id, **coupon_data)

    def delete(self, session: Session, coupon_id: int) -> bool:
        """删除满减券"""
        return super().delete(session, coupon_id)


class CouponUsageRecordDAO(DAO):
    """优惠券使用记录数据访问对象"""

    def __init__(self):
        super().__init__(CouponUsageRecord)

    def create(self, session: Session, record: CouponUsageRecordCreate) -> CouponUsageRecord:
        """创建使用记录"""
        record_data = record.model_dump()
        return super().create(session, **record_data)

    def batch_create(self, session: Session, records: List[CouponUsageRecordCreate]) -> dict:
        """批量创建使用记录
        
        Args:
            session: 数据库会话
            records: 优惠券使用记录列表
            
        Returns:
            dict: 包含成功创建数量、失败数量和错误信息的字典
        """
        success_count = 0
        failed_count = 0
        errors = []
        created_records = []
        
        for i, record in enumerate(records):
            try:
                record_data = record.model_dump()
                db_record = self.model(**record_data)
                session.add(db_record)
                session.flush()  # 刷新以获取ID，但不提交
                created_records.append(db_record)
                success_count += 1
            except Exception as e:
                failed_count += 1
                errors.append({
                    "index": i,
                    "record": record.model_dump(),
                    "error": str(e)
                })
        
        try:
            session.commit()  # 统一提交所有成功的记录
            # 刷新所有创建的记录以获取完整信息
            for record in created_records:
                session.refresh(record)
            
            # 将数据库对象转换为可序列化的字典
            serialized_records = []
            for record in created_records:
                serialized_records.append({
                    "id": record.id,
                    "coupon_id": record.coupon_id,
                    "user_id": record.user_id,
                    "order_id": record.order_id,
                    "used_at": record.used_at.strftime("%Y-%m-%d %H:%M:%S") if record.used_at else None,
                    "created_at": record.created_at.strftime("%Y-%m-%d %H:%M:%S") if record.created_at else None,
                    "updated_at": record.updated_at.strftime("%Y-%m-%d %H:%M:%S") if record.updated_at else None,
                    "status": record.status.value if record.status else None
                })
                
        except Exception as e:
            session.rollback()
            return {
                "success_count": 0,
                "failed_count": len(records),
                "total_count": len(records),
                "errors": [{"error": f"批量提交失败: {str(e)}"}],
                "created_records": []
            }
        
        return {
            "success_count": success_count,
            "failed_count": failed_count,
            "total_count": len(records),
            "errors": errors,
            "created_records": serialized_records
        }

    def get(self, session: Session, record_id: int) -> Optional[CouponUsageRecord]:
        """根据ID获取使用记录"""
        return super().get(session, record_id)

    def get_by_order(self, session: Session, order_id: int) -> List[CouponUsageRecord]:
        """获取订单的优惠券使用记录"""
        return session.query(self.model).filter(self.model.order_id == order_id).all()

    def get_by_user(self, session: Session, user_id: int) -> List[CouponUsageRecord]:
        """获取用户的优惠券使用记录"""
        return session.query(self.model).filter(self.model.user_id == user_id).all()

    def search_records(
            self,
            session: Session,
            search_params: CouponUsageRecordSearch,
            skip: int = 0,
            limit: int = 100
    ) -> dict:
        """搜索优惠券使用记录
        
        Args:
            session: 数据库会话
            search_params: 搜索参数
            skip: 分页起始位置
            limit: 每页数量
            
        Returns:
            dict: 包含 'total'（搜索结果总数）和 'list'（分页后的记录列表）的字典
        """
        query = session.query(self.model).join(
            Coupon, Coupon.id == self.model.coupon_id
        ).join(
            User, User.id == self.model.user_id
        )

        if search_params.coupon_name:
            query = query.filter(Coupon.name.ilike(f"%{search_params.coupon_name}%"))

        if search_params.username:
            query = query.filter(User.username.ilike(f"%{search_params.username}%"))

        if search_params.order_id:
            query = query.filter(self.model.order_id == search_params.order_id)

        total = query.count()
        items = query.offset(skip).limit(limit).all()

        return {
            "total": total,
            "list": items
        }


# 创建 DAO 实例
coupon_dao = CouponDAO()
discount_coupon_dao = DiscountCouponDAO()
cash_coupon_dao = CashCouponDAO()
full_reduction_coupon_dao = FullReductionCouponDAO()
coupon_usage_record_dao = CouponUsageRecordDAO()
