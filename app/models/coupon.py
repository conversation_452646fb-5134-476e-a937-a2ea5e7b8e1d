import enum
from datetime import datetime

from sqlalchemy import Column, Integer, String, Text, Enum, DateTime, ForeignKey, Float, Boolean
from sqlalchemy.orm import relationship

from app.db.base_class import Base
from app.models.enum import Status


class CouponType(enum.Enum):
    """优惠券类型枚举
    定义了系统中所有支持的优惠券类型
    """
    COUPON = "coupon"  # 基础优惠券
    DISCOUNT = "discount"  # 折扣券
    CASH = "cash"  # 现金券
    FULL_REDUCTION = "full_reduction"  # 满减券


class CouponScope(enum.Enum):
    """优惠券作用范围枚举
    定义了优惠券的作用范围
    """
    ORDER = "order"  # 订单范围
    PRODUCT = "product"  # 商品范围


class CouponUsageStatus(enum.Enum):
    """优惠券使用状态枚举
    定义了优惠券的使用状态
    """
    VALID = "valid"  # 有效
    INVALID = "invalid"  # 无效
    NOT_STARTED = "not_started"  # 未生效
    EXPIRED = "expired"  # 已过期
    USED = "used"  # 已使用

class CouponUsageCycle(enum.Enum):
    """优惠券使用周期枚举
    定义了优惠券的使用周期
    """
    PER_ORDER = "per_order"  # 每次订单
    PER_DAY = "per_day"  # 每天
    PER_WEEK = "per_week"  # 每周
    PER_MONTH = "per_month"  # 每月
    PER_YEAR = "per_year"  # 每年

class CouponUsageRecord(Base):
    """优惠券使用记录
    记录优惠券的使用情况，包括使用者、使用时间、使用订单等信息
    """
    __tablename__ = "coupon_usage_records"

    id = Column(Integer, primary_key=True, index=True, comment="记录ID")
    coupon_id = Column(Integer, ForeignKey("coupons.id"), nullable=False, comment="优惠券ID")
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, comment="使用者ID")
    order_id = Column(Integer, ForeignKey("orders.id"), nullable=True, default=None, comment="订单ID")
    used_at = Column(DateTime, default=datetime.now, comment="使用时间")
    created_at = Column(DateTime, default=datetime.now, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment="更新时间")
    status = Column(Enum(CouponUsageStatus), nullable=False, default=CouponUsageStatus.VALID, comment="优惠券使用状态")
    # 关联关系
    user = relationship("User", back_populates="coupon_usage_records")
    coupon = relationship("Coupon", back_populates="usage_records")
    order = relationship("Order", back_populates="coupon_usage_records")


class Coupon(Base):
    """优惠券基类
    所有具体的优惠券类型都继承自这个类
    """
    __tablename__ = "coupons"

    id = Column(Integer, primary_key=True, index=True, comment="优惠券ID")
    name = Column(String(100), nullable=False, comment="优惠券名称")
    description = Column(Text, comment="优惠券描述")
    start_time = Column(DateTime, nullable=True, default=datetime.now, comment="优惠券生效时间")
    end_time = Column(DateTime, nullable=True, default=datetime.now, comment="优惠券结束时间")
    scope = Column(Enum(CouponScope), nullable=False, default=CouponScope.ORDER, comment="优惠券作用范围")
    is_mutual_exclusive = Column(Boolean, nullable=False, default=False, comment="是否互斥")
    status = Column(Enum(Status), nullable=False, default=Status.ACTIVE, comment="优惠券状态")
    type = Column(Enum(CouponType), nullable=False, default=CouponType.COUPON, comment="优惠券类型")
    quantity = Column(Integer, nullable=False, default=0, comment="优惠券数量")
    usage_cycle = Column(Enum(CouponUsageCycle), nullable=True, default=CouponUsageCycle.PER_ORDER, comment="使用周期")
    usage_limit = Column(Integer, nullable=True, default=0, comment="使用限制")
    created_at = Column(DateTime, default=datetime.now, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment="更新时间")

    # 关联
    usage_records = relationship("CouponUsageRecord", back_populates="coupon")

    __mapper_args__ = {
        "polymorphic_on": type,
        "polymorphic_identity": CouponType.COUPON
    }


class DiscountCoupon(Coupon):
    """折扣券
    用于按比例折扣的优惠券
    """
    __tablename__ = "discount_coupons"

    id = Column(Integer, ForeignKey("coupons.id"), primary_key=True, comment="关联的基础优惠券ID")
    discount_rate = Column(Float, nullable=False, default=1, comment="折扣比例（0-1之间的小数）")
    min_amount = Column(Float, nullable=False, default=0, comment="最低消费金额")
    max_discount = Column(Float, nullable=False, default=0, comment="最大折扣金额")

    __mapper_args__ = {
        "polymorphic_identity": CouponType.DISCOUNT
    }


class CashCoupon(Coupon):
    """现金券
    用于直接抵扣金额的优惠券
    """
    __tablename__ = "cash_coupons"

    id = Column(Integer, ForeignKey("coupons.id"), primary_key=True, comment="关联的基础优惠券ID")
    amount = Column(Float, nullable=False, default=0, comment="抵扣金额")

    __mapper_args__ = {
        "polymorphic_identity": CouponType.CASH
    }


class FullReductionCoupon(Coupon):
    """满减券
    用于满额减价的优惠券
    """
    __tablename__ = "full_reduction_coupons"

    id = Column(Integer, ForeignKey("coupons.id"), primary_key=True, comment="关联的基础优惠券ID")
    full_amount = Column(Float, nullable=False, default=0, comment="满足金额")
    reduction_amount = Column(Float, nullable=False, default=0, comment="满减金额")

    __mapper_args__ = {
        "polymorphic_identity": CouponType.FULL_REDUCTION
    }
