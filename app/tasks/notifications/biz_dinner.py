from sqlalchemy.orm import joinedload

from app.dao.admin import get_notify_admins
from app.db.session import SessionLocal
from app.events.handlers import logger
from app.events.models import OrderEventAction
from app.models.order import Order, OrderItem
from app.models.reservation import BizReservationRequest
from .utils import send_dingtalk_message, send_miniapp_message


def _format_reservation_period(reservation_period: str) -> str:
    """
    格式化预订时段字符串
    
    Args:
        reservation_period: 格式如 "2507251340_2507251640" 的字符串
        
    Returns:
        str: 格式化后的时间字符串，如 "25年7月25日 13点40分"，空值时返回空字符串
    """
    if not reservation_period or reservation_period == "":
        return ""
    
    try:
        # 取第一部分时间
        first_time = reservation_period.split('_')[0]
        
        # 解析时间格式 YYMMDDHHMIN
        if len(first_time) == 10:
            year = first_time[:2]
            month = first_time[2:4]
            day = first_time[4:6]
            hour = first_time[6:8]
            minute = first_time[8:10]
            
            return f"20{year}年{int(month)}月{int(day)}日 {int(hour)}:{int(minute)}"
        else:
            return reservation_period  # 如果格式不对，返回原值
    except Exception:
        return reservation_period  # 解析失败时返回原值


async def send_biz_reservation_notification(order_id: int, order_action: OrderEventAction):
    """
    检查订单是否有对应的商务餐预订请求，如果有则获取详细信息并打印到日志

    Args:
        order_id: 订单ID
        order_action: 订单动作
    """
    if not order_id:
        logger.debug("订单ID为空，跳过商务餐预订请求检查")
        return

    db = SessionLocal()
    try:
        # 查询是否存在对应的BizReservationRequest
        biz_reservation = db.query(BizReservationRequest).filter(
            BizReservationRequest.orders_id == order_id
        ).first()

        if not biz_reservation:
            logger.debug(f"订单ID {order_id} 没有对应的商务餐预订请求")
            return

        logger.debug("️ " + "=" * 60)
        logger.debug("️ 发现商务餐预订请求，开始获取详细信息")
        logger.debug("️ " + "=" * 60)

        # 获取订单详细信息
        order = db.query(Order).options(
            joinedload(Order.items).joinedload(OrderItem.product)
        ).filter(Order.id == order_id).first()

        if not order:
            logger.error(f" 未找到订单ID {order_id} 的订单信息")
            return

        # 构建订单项目内容明细
        order_items_detail = []
        for item in order.items:
            item_detail = {
                "产品名称": item.product.name if item.product else "未知产品",
                "数量": item.quantity,
                "单价": item.price,
                "小计": item.subtotal,
                "明细": item.product.description
            }
            order_items_detail.append(item_detail)

        # 组合所有信息
        reservation_order_info = {
            "订单信息": {
                "订单ID": order.id,
                "订单号": order.order_no,
                "预订时间": biz_reservation.reservation_time.strftime(
                    "%Y-%m-%d %H:%M:%S") if biz_reservation.reservation_time else "未设置",
                "订单金额": order.total_amount,
                "应付金额": order.payable_amount,
                "实际支付金额": order.actual_amount_paid,
                "支付状态": order.payment_status.value if order.payment_status else "未知",
                "订单状态": order.status.value if order.status else "未知"
            },
            "商务餐预订请求信息": {
                "预订ID": biz_reservation.id,
                "联系人姓名": biz_reservation.name or "未填写",
                "联系电话": biz_reservation.phone or "未填写",
                "预订人数": biz_reservation.persons or 0,
                "备注": biz_reservation.remark or "无备注",
                "预订时段": _format_reservation_period(biz_reservation.reservation_period),
                "就餐开始时间": biz_reservation.dining_start_time.strftime(
                    "%Y-%m-%d %H:%M:%S") if biz_reservation.dining_start_time else "未设置",
                "就餐结束时间": biz_reservation.dining_end_time.strftime(
                    "%Y-%m-%d %H:%M:%S") if biz_reservation.dining_end_time else "未设置"
            },
            "订单项目内容明细": order_items_detail
        }

        # 生成同样格式的完整数据结构文本
        logger.debug(" " + "=" * 60)
        logger.debug(" 完整商务餐预订订单数据结构")
        logger.debug(" " + "=" * 60)

        notify_users = get_notify_admins(db)
        try:
            await _send_miniapp_message(reservation_order_info, order_action, notify_users)
        except Exception as send_error:
            logger.error(f"发送微信小程序消息失败： {str(send_error)}")

        try:
            await _send_dingtalk_message(reservation_order_info, order_action)
        except Exception as send_error:
            logger.error(f"发送钉钉消息失败： {str(send_error)}")

    except Exception as e:
        logger.error(f" 查询商务餐预订请求时发生错误: {str(e)}")
        logger.exception("详细错误信息:")
    finally:
        db.close()


async def _send_miniapp_message(reservation_order_info: dict, order_action: OrderEventAction,
                                notify_users: list):
    try:
        order_info = reservation_order_info.get("订单信息", {})
        biz_info = reservation_order_info.get("商务餐预订请求信息", {})

        if order_action == OrderEventAction.PAID:
            # 商务餐预订并支付成功后发出的消息，服务号模版消息“”
            template_id = "WYycb9FEm8qgpPHpo1XTYTW2L46CAEaY7q53bW5SAhA"

            template_data = {
                "thing12": {"value": "商务餐预订"},  # 预约项目
                "thing15": {"value": biz_info.get("联系人姓名", "未填写")},  # 预约人
                "phone_number16": {"value": biz_info.get("联系电话", "未填写")},  # 联系电话
                "time11": {"value": biz_info.get("预订时段", "")},  # 预约时间
                "character_string14": {"value": str(biz_info.get("预订人数", 0))}  # 预约人数
            }

        elif order_action == OrderEventAction.CANCELLED:
            # 取消商务餐预订后发送的消息，服务号模版消息“”
            template_id = "UW1kWNvlMHzlQY58sj1z1BnFpbF56JQlPH0n-hag4YE"

            template_data = {
                "thing46": {"value": "取消商务餐预订"},  # 取消项目
                "thing10": {"value": biz_info.get("联系人姓名", "未填写")},  # 预约人
                "phone_number26": {"value": biz_info.get("联系电话", "未填写")},  # 联系电话
                "time14": {"value": biz_info.get("预订时段", "")},  # 预约时间
                "character_string47": {"value": str(biz_info.get("预订人数", 0))}  # 预约人数
            }
        else:
            logger.info("未定义订单操作的对应操作行为")
            return

        await send_miniapp_message(
            recipients=notify_users,
            template_id=template_id,
            template_data=template_data
        )

        logger.info("商务餐预订小程序消息发送成功")

    except Exception as e:
        logger.error(f"发送商务餐订餐消息失败: {e}")


def _generate_short_messages(reservation_order_info: dict, title: str) -> list:
    """
    生成简短的钉钉消息内容
    
    Args:
        reservation_order_info: 商务餐预订订单信息字典
        
    Returns:
        list: 简短消息行列表
    """
    short_messages = []
    short_messages.append(f"{title}")

    # 订单号
    order_no = reservation_order_info["订单信息"].get("订单号", "未知")
    short_messages.append(f"订单号: {order_no}")

    # 联系人姓名
    contact_name = reservation_order_info["商务餐预订请求信息"].get("联系人姓名", "未填写")
    short_messages.append(f"联系人: {contact_name}")

    # 联系电话
    contact_phone = reservation_order_info["商务餐预订请求信息"].get("联系电话", "未填写")
    short_messages.append(f"联系电话: {contact_phone}")

    # 预订人数
    persons = reservation_order_info["商务餐预订请求信息"].get("预订人数", 0)
    short_messages.append(f"预订人数: {persons}人")

    # 约定时段
    reservation_period = reservation_order_info["商务餐预订请求信息"].get("预订时段", "")
    short_messages.append(f"预订时段: {reservation_period}")

    # 备注
    remark = reservation_order_info["商务餐预订请求信息"].get("备注", "无备注")
    short_messages.append(f"备注: {remark}")

    # 订单项目内容明细
    short_messages.append("### 预订内容明细:")
    for i, item in enumerate(reservation_order_info["订单项目内容明细"], 1):
        product_name = item.get("产品名称", "未知产品")
        quantity = item.get("数量", 0)
        price = item.get("单价", 0)
        subtotal = item.get("小计", 0)
        description = item.get("明细", "")
        short_messages.append(f"{i}. {product_name}:{price} × {quantity} = ¥{subtotal}")
        if isinstance(description, str):
            description_lines = description.split("\n")
            for line in description_lines:
                line = ". " + line.strip('\r').strip('\n')
                short_messages.append(line)
    return short_messages


def _generate_detailed_messages(reservation_order_info: dict, title: str) -> list:
    """
    生成详细的钉钉消息内容
    
    Args:
        reservation_order_info: 商务餐预订订单信息字典
        
    Returns:
        list: 详细消息行列表
    """
    messages = []
    messages.append("️ " + "=" * 20)
    messages.append(f"️ {title}")
    messages.append("️ " + "=" * 20)

    # 订单信息部分
    messages.append(" 订单信息:")
    for key, value in reservation_order_info["订单信息"].items():
        messages.append(f"️   {key}: {value}")

    # 商务餐预订请求信息部分
    messages.append("   商务餐预订请求信息:")
    for key, value in reservation_order_info["商务餐预订请求信息"].items():
        messages.append(f"️   {key}: {value}")

    # 订单项目明细部分
    messages.append("   预订内容明细:")
    for i, item in enumerate(reservation_order_info["订单项目内容明细"], 1):
        messages.append(f"️   项目 {i}:")
        for key, value in item.items():
            messages.append(f"️     {key}: {value}")

    messages.append("️ " + "=" * 20)
    messages.append("️ 通知时间: " + str(reservation_order_info["订单信息"]["预订时间"]))
    messages.append("️ " + "=" * 20)

    return messages


async def _send_dingtalk_message(reservation_order_info: dict, order_action: OrderEventAction):
    """
    发送钉钉通知，使用与日志相同的格式

    Args:
        reservation_order_info: 商务餐预订订单信息字典
    """
    try:
        logger.info(" " + "=" * 20)
        logger.info(" 开始生成钉钉通知消息")
        logger.info(" " + "=" * 20)
        title = ""

        if order_action == OrderEventAction.PAID:
            title = "商务餐预订通知"
            messages = _generate_detailed_messages(reservation_order_info, "")
            short_messages = _generate_short_messages(reservation_order_info, "")
        elif order_action == OrderEventAction.CANCELLED:
            title = "商务餐预订取消通知"
            messages = _generate_detailed_messages(reservation_order_info, "")
            messages.append("**请注意：该订单已被取消**")
            short_messages = _generate_short_messages(reservation_order_info, "")
            short_messages.append("**请注意：该订单已被取消**")
        else:
            messages = _generate_detailed_messages(reservation_order_info, "")
            short_messages = _generate_short_messages(reservation_order_info, "")

        # 将消息行合并为完整文本
        notification_text = "  \n  ".join(messages)
        short_notification_text = "  \n  ".join(short_messages)

        # 打印生成的通知文本到日志
        logger.info(" 生成的钉钉通知文本:")
        logger.info(" " + "-" * 20)
        for line in messages:
            logger.info(f" {line}")
        logger.info(" " + "-" * 20)

        logger.info(f" 发送钉钉消息: {short_notification_text}")
        # 发送钉钉消息
        result = await send_dingtalk_message(
            title=title,
            body=short_notification_text
        )

        if result.success_count > 0:
            logger.info("  钉钉通知发送成功")
        else:
            logger.error("  钉钉通知发送失败")

        logger.info(" " + "=" * 60)
        logger.info(" 钉钉通知处理完成")
        logger.info(" " + "=" * 60)

    except Exception as e:
        logger.error(f"  发送钉钉通知时发生错误: {str(e)}")
        logger.exception("详细错误信息:")
