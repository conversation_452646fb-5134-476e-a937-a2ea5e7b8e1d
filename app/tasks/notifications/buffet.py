from datetime import datetime, timedelta
from typing import List, Dict, Any

from sqlalchemy.orm import Session

from app.dao.admin import get_notify_admins
from app.db.session import SessionLocal
from app.service.report import report_service
from app.utils.logger import logger
from app.tasks.notifications.utils import send_dingtalk_message, send_miniapp_message


def classify_reservation_statistics(reservation_list: List[Dict[str, Any]]) -> Dict[str, int]:
    """
    对预订报表数据进行分类统计处理

    Args:
        reservation_list: 预订记录列表，来自get_reservation_report_raw的返回结果

    Returns:
        Dict[str, int]: 包含dinner和lunch统计数据的字典
    """
    # 初始化统计结果
    statistics = {
        "dinner": 0,
        "lunch": 0
    }

    # 按product_id分组统计quantity
    product_stats = {}

    for record in reservation_list:
        # 获取预订时段
        reservation_period = record.get('reservation_period', '')
        product_id = record.get('product_id', 0)
        quantity = record.get('quantity', 0)

        # 跳过无效记录
        if not reservation_period or not product_id or quantity <= 0:
            continue

        # 解析预订时段，判断是午餐还是晚餐
        # 格式：2507161105_2507161115 (YYMMDDHHmm_YYMMDDHHmm)
        if "_" in reservation_period:
            start_time = reservation_period.split("_")[0]
            if len(start_time) >= 10:
                # 提取小时部分 (第6-8位)
                hour_str = start_time[6:8]
                try:
                    hour = int(hour_str)
                    # 判断是午餐还是晚餐：大于16:00为dinner，小于等于16:00为lunch
                    meal_type = "dinner" if hour > 16 else "lunch"

                    # 按product_id和meal_type分组统计
                    key = f"{product_id}_{meal_type}"
                    if key not in product_stats:
                        product_stats[key] = 0
                    product_stats[key] += quantity

                except ValueError:
                    # 如果小时解析失败，跳过该记录
                    continue

    # 汇总统计结果
    for key, total_quantity in product_stats.items():
        if "_dinner" in key:
            statistics["dinner"] += total_quantity
        elif "_lunch" in key:
            statistics["lunch"] += total_quantity

    return statistics


def get_statistic_data(db: Session, dining_start_time: str, dining_end_time: str):
    """
    获取统计数据

    Args:
        db: 数据库会话
        dining_start_time: 就餐开始时间，格式: "2025-07-16 00:00:00"
        dining_end_time: 就餐结束时间，格式: "2025-07-16 23:59:59"

    Returns:
        tuple: (预订记录列表, 总记录数)
    """
    # 创建数据库会话

    try:
        # 将字符串时间转换为 datetime 对象
        dining_start_dt = datetime.strptime(dining_start_time, "%Y-%m-%d %H:%M:%S")
        dining_end_dt = datetime.strptime(dining_end_time, "%Y-%m-%d %H:%M:%S")

        # 调用 get_reservation_report_raw 函数
        # 获取已支付、核销以及自动核销的所有记录
        reservation_list, total_count = report_service.get_reservation_report_raw(
            db=db,
            dining_start_time=dining_start_dt,
            dining_end_time=dining_end_dt,
            page=1,
            page_size=10000,
            status=["PAID_FULL", "VERIFIED", "AUTO_VERIFIED"]
        )

        classify_result = classify_reservation_statistics(reservation_list)

        return reservation_list, total_count, classify_result

    except Exception as e:
        logger.error(f"获取统计数据失败: {e}")
        return [], 0
    finally:
        db.close()


async def send_daily_buffet_statistic_notification(meal_type: str, day_type: str):
    template_id = "WYycb9FEm8qgpPHpo1XTYejQp_2H7Wilsx1IGrB4GDo"

    db = SessionLocal()
    try:
        notify_users = get_notify_admins(db)

        # 根据day_type计算目标日期
        if day_type.lower() == "today":
            target_date = datetime.now().date()
        elif day_type.lower() == "tomorrow":
            target_date = (datetime.now() + timedelta(days=1)).date()
        else:
            logger.error(f"不支持的day_type: {day_type}")
            return

        # 生成当天的开始和结束时间
        dining_start_time = f"{target_date} 00:00:00"
        dining_end_time = f"{target_date} 23:59:59"
        target_date_str = target_date.strftime("%Y年%m月%d日")

        # 获取统计数据
        reservation_list, total_count, classify_result = get_statistic_data(db, dining_start_time, dining_end_time)

        result = []
        messages = []
        if meal_type.lower() == "lunch" and day_type.lower() == "today":
            logger.info("今日自助餐午餐订单统计：{} 人 {}".format(classify_result["lunch"], datetime.now()))
            await send_miniapp_message(
                recipients=notify_users,
                template_id=template_id,
                template_data={
                    "time11": {"value": target_date_str},
                    "thing12": {"value": "今日自助餐午餐预订人数"},
                    "character_string14": {"value": classify_result["lunch"]}
                }
            )
            #   messages.append("️自助餐点餐统计")
            #   messages.append("️ 今日自助餐午餐预订人数:{} 人".format(str(classify_result["lunch"])))
            # notification_text = "\n".join(  messages)
            # # 发送钉钉消息
            # result = await send_dingtalk_message(
            #     title="",
            #     body=notification_text
            # )
            notification_text = "️> 预订人数: **{}** 人".format(str(classify_result["lunch"]))
            # 发送钉钉消息
            result = await send_dingtalk_message(
                title="今日自助餐午餐预订人数",
                body=notification_text
            )


        elif meal_type.lower() == "dinner" and day_type.lower() == "today":
            logger.info("今日自助餐晚餐订单统计：{} 人 {}".format(classify_result["dinner"], datetime.now()))
            await send_miniapp_message(
                recipients=notify_users,
                template_id=template_id,
                template_data={
                    "time11": {"value": target_date_str},
                    "thing12": {"value": "今日自助餐晚餐预订人数"},
                    "character_string14": {"value": classify_result["dinner"]}
                }
            )
            #   messages.append("️自助餐点餐统计")
            #   messages.append("️ 今日自助餐晚餐预订人数:{} 人".format(str(classify_result["dinner"])))
            # notification_text = "\n".join(  messages)
            # # 发送钉钉消息
            # result = await send_dingtalk_message(
            #     title="",
            #     body=notification_text
            # )

            notification_text = "️> 预订人数: **{}** 人".format(str(classify_result["dinner"]))
            # 发送钉钉消息
            result = await send_dingtalk_message(
                title="今日自助餐晚餐预订人数",
                body=notification_text
            )


        elif meal_type.lower() == "lunch" and day_type.lower() == "tomorrow":
            logger.info("明日自助餐午餐订单统计：{} 人 {}".format(classify_result["lunch"], datetime.now()))
            await send_miniapp_message(
                recipients=notify_users,
                template_id=template_id,
                template_data={
                    "time11": {"value": target_date_str},
                    "thing12": {"value": "明日自助餐午餐预订人数"},
                    "character_string14": {"value": classify_result["lunch"]}
                }
            )
            #   messages.append("️自助餐点餐统计")
            #   messages.append("️ 明日自助餐午餐预订人数:{} 人".format(str(classify_result["lunch"])))
            # notification_text = "\n".join(  messages)
            # # 发送钉钉消息
            # result = await send_dingtalk_message(
            #     title="",
            #     body=notification_text
            # )

            notification_text = "️> 预订人数: **{}** 人".format(str(classify_result["lunch"]))
            # 发送钉钉消息
            result = await send_dingtalk_message(
                title="明日自助餐午餐预订人数",
                body=notification_text
            )


        elif meal_type.lower() == "dinner" and day_type.lower() == "tomorrow":
            logger.info("明日自助餐晚餐订单统计：{} 人 {}".format(classify_result["dinner"], datetime.now()))
            await send_miniapp_message(
                recipients=notify_users,
                template_id=template_id,
                template_data={
                    "time11": {"value": target_date_str},
                    "thing12": {"value": "明日自助餐晚餐预订人数"},
                    "character_string14": {"value": classify_result["dinner"]}
                }
            )
            #   messages.append("️自助餐点餐统计")
            #   messages.append("️ 明日自助餐晚餐预订人数:{} 人".format(str(classify_result["dinner"])))
            # notification_text = "\n".join(  messages)
            # # 发送钉钉消息
            # result = await send_dingtalk_message(
            #     title="",
            #     body=notification_text
            # )

            notification_text = "️> 预订人数: **{}** 人".format(str(classify_result["dinner"]))
            # 发送钉钉消息
            result = await send_dingtalk_message(
                title="明日自助餐晚餐预订人数",
                body=notification_text
            )

        if result.success_count > 0:
            logger.info("      钉钉通知发送成功")
        else:
            logger.error("      钉钉通知发送失败")
    except Exception as e:
        logger.error(f"发送自助餐订单统计消息失败: {e}")
    finally:
        db.close()
